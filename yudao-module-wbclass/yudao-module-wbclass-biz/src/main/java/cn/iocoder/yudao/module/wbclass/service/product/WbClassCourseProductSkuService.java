package cn.iocoder.yudao.module.wbclass.service.product;

import cn.iocoder.yudao.module.wbclass.controller.admin.product.vo.WbClassCourseProductSkuCreateReqVO;
import cn.iocoder.yudao.module.wbclass.controller.admin.product.vo.WbClassCourseProductSkuUpdateReqVO;
import cn.iocoder.yudao.module.wbclass.controller.admin.product.vo.WbClassCourseProductSkuVO;
import cn.iocoder.yudao.module.wbclass.dal.dataobject.course.WbClassCourseDO;
import cn.iocoder.yudao.module.wbclass.dal.dataobject.product.WbClassCourseProductSkuDO;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 课程商品SKU Service 接口
 *
 * <AUTHOR>
 */
public interface WbClassCourseProductSkuService {

    /**
     * 创建SKU
     *
     * @param createReqVO 创建信息
     * @return SKU ID
     */
    Long createSku(WbClassCourseProductSkuCreateReqVO createReqVO);

    /**
     * 更新SKU
     *
     * @param updateReqVO 更新信息
     */
    void updateSku(WbClassCourseProductSkuUpdateReqVO updateReqVO);

    /**
     * 删除SKU
     *
     * @param id SKU ID
     */
    void deleteSku(Long id);

    /**
     * 根据商品ID获取SKU列表
     *
     * @param productId 商品ID
     * @return SKU列表
     */
    List<WbClassCourseProductSkuDO> getSkuListByProductId(Long productId);

    /**
     * 获取SKU
     *
     * @param skuId SKU ID
     * @return SKU
     */
    WbClassCourseProductSkuDO getSku(Long skuId);

    /**
     * 保存商品的SKU列表
     *
     * @param productId 商品ID
     * @param skuVOList SKU VO列表
     */
    void saveSkuList(Long productId, List<WbClassCourseProductSkuVO> skuVOList);

    /**
     * 删除商品的所有SKU
     *
     * @param productId 商品ID
     */
    void deleteSkusByProductId(Long productId);

    /**
     * 根据SKU ID获取关联的课程列表
     *
     * @param skuId SKU ID
     * @return 课程列表
     */
    List<WbClassCourseDO> getCoursesBySkuId(Long skuId);

    /**
     * 管理SKU课程关联
     *
     * @param skuId SKU ID
     * @param courseIds 课程ID列表
     */
    void manageSkuCourseRelations(Long skuId, List<Long> courseIds);

    /**
     * 增加SKU销量
     *
     * @param skuId SKU ID
     */
    void incrementSalesCount(Long skuId);

    /**
     * 根据课程ID获取关联的SKU列表
     *
     * @param courseId 课程ID
     * @return SKU列表
     */
    List<WbClassCourseProductSkuDO> getSkusByCourseId(Long courseId);

    /**
     * 根据课程ID列表批量获取关联的SKU列表
     *
     * @param courseIds 课程ID列表
     * @return 课程ID到SKU列表的映射
     */
    Map<Long, List<WbClassCourseProductSkuDO>> getSkusByCourseIds(Collection<Long> courseIds);

}