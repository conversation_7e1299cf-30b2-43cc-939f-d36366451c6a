package cn.iocoder.yudao.module.wbclass.controller.app.wbclasscourseproduct.vo;

import cn.iocoder.yudao.module.wbclass.controller.admin.product.vo.WbClassCourseProductSkuRespVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户 App - 训练营课程商品 Response VO
 *
 * <AUTHOR>
 */
@ApiModel("用户 App - 训练营课程商品 Response VO")
@Data
public class AppWbClassCourseProductRespVO {

    @ApiModelProperty(value = "商品ID", example = "1024")
    private Long id;

    @ApiModelProperty(value = "商品名称", example = "Java全栈开发训练营")
    private String name;

    @ApiModelProperty(value = "商品描述", example = "这是一个全面的Java开发商品...")
    private String description;

    @ApiModelProperty(value = "商品封面图片", example = "https://example.com/cover.jpg")
    private String coverUrl;

    @ApiModelProperty(value = "原价（分）", example = "19900")
    private Integer originalPrice;

    @ApiModelProperty(value = "售价（分）", example = "9900")
    private Integer salePrice;

    @ApiModelProperty(value = "浏览次数", example = "1000")
    private Integer viewCount;

    @ApiModelProperty(value = "销售数量", example = "100")
    private Integer salesCount;

    @ApiModelProperty(value = "状态：1-上架，2-下架", example = "1")
    private Integer status;

    @ApiModelProperty(value = "排序", example = "1")
    private Integer sort;

    @ApiModelProperty(value = "创建时间", example = "2024-01-01 12:00:00")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "SKU列表")
    private List<AppWbClassCourseProductSkuRespVO> skus;

}
