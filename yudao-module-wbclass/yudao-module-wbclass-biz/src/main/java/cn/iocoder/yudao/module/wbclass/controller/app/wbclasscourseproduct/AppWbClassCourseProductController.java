package cn.iocoder.yudao.module.wbclass.controller.app.wbclasscourseproduct;

import cn.hutool.core.net.Ipv4Util;
import cn.hutool.extra.servlet.ServletUtil;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.security.core.annotations.PreAuthenticated;
import cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.yudao.framework.web.core.util.WebFrameworkUtils;
import cn.iocoder.yudao.module.wbclass.controller.app.wbclasscourseproduct.vo.AppWbClassCourseProductOrderCreateReqVO;
import cn.iocoder.yudao.module.wbclass.controller.app.wbclasscourseproduct.vo.AppWbClassCourseProductPageReqVO;
import cn.iocoder.yudao.module.wbclass.controller.app.wbclasscourseproduct.vo.AppWbClassCourseProductRespVO;
import cn.iocoder.yudao.module.wbclass.controller.app.wbclasscourseproduct.vo.AppWbClassCourseProductSkuRespVO;
import cn.iocoder.yudao.module.wbclass.convert.product.WbClassCourseProductConvert;
import cn.iocoder.yudao.module.wbclass.convert.product.WbClassCourseProductSkuConvert;
import cn.iocoder.yudao.module.wbclass.dal.dataobject.product.WbClassCourseProductDO;
import cn.iocoder.yudao.module.wbclass.dal.dataobject.product.WbClassCourseProductSkuDO;
import cn.iocoder.yudao.module.wbclass.service.course.WbClassCourseOrderService;
import cn.iocoder.yudao.module.wbclass.service.product.WbClassCourseProductService;
import cn.iocoder.yudao.module.wbclass.service.product.WbClassCourseProductSkuService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.List;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

/**
 * 用户App - 训练营课程商品 Controller
 *
 * <AUTHOR>
 */
@Api(tags = "用户App - 训练营课程商品")
@RestController
@RequestMapping("/wbclass/course-product")
@Validated
public class AppWbClassCourseProductController {

    @Resource
    private WbClassCourseProductService productService;

    @Resource
    private WbClassCourseProductSkuService skuService;

    @Resource
    private WbClassCourseOrderService orderService;

    @GetMapping("/page")
    @ApiOperation("获得课程商品分页")
    public CommonResult<PageResult<AppWbClassCourseProductRespVO>> getProductPage(@Valid AppWbClassCourseProductPageReqVO pageVO) {
        PageResult<WbClassCourseProductDO> pageResult = productService.getAppProductPage(pageVO);
        PageResult<AppWbClassCourseProductRespVO> result = WbClassCourseProductConvert.INSTANCE.convertAppPage(pageResult);

        // 为每个商品获取SKU列表
        for (AppWbClassCourseProductRespVO respVO : result.getList()) {
            List<WbClassCourseProductSkuDO> skuList = skuService.getSkuListByProductId(respVO.getId());
            List<AppWbClassCourseProductSkuRespVO> appSkuList = WbClassCourseProductSkuConvert.INSTANCE.convertToAppRespVOList(skuList);

            // 为每个SKU获取关联的课程
            for (AppWbClassCourseProductSkuRespVO skuRespVO : appSkuList) {
                skuRespVO.setCourses(skuService.getCoursesBySkuId(skuRespVO.getId()));
            }

            respVO.setSkus(appSkuList);
        }

        return success(result);
    }

    @GetMapping("/get")
    @ApiOperation("获得课程商品详情")
    @ApiImplicitParam(name = "id", value = "编号", required = true, example = "1024", dataTypeClass = Long.class)
    public CommonResult<AppWbClassCourseProductRespVO> getProduct(@RequestParam("id") Long id) {
        // 增加浏览量
        productService.incrementViewCount(id);

        WbClassCourseProductDO product = productService.getProduct(id);
        AppWbClassCourseProductRespVO respVO = WbClassCourseProductConvert.INSTANCE.convertApp(product);

        // 获取SKU列表
        List<WbClassCourseProductSkuDO> skuList = skuService.getSkuListByProductId(id);
        List<AppWbClassCourseProductSkuRespVO> appSkuList = WbClassCourseProductSkuConvert.INSTANCE.convertToAppRespVOList(skuList);

        // 为每个SKU获取关联的课程
        for (AppWbClassCourseProductSkuRespVO skuRespVO : appSkuList) {
            skuRespVO.setCourses(skuService.getCoursesBySkuId(skuRespVO.getId()));
        }

        respVO.setSkus(appSkuList);

        return success(respVO);
    }

    @PostMapping("/order/create")
    @ApiOperation("创建课程商品订单")
    @PreAuthenticated
    public CommonResult<Long> createOrder(@Valid @RequestBody AppWbClassCourseProductOrderCreateReqVO createReqVO,
                                          HttpServletRequest request) {
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        String userIp = ServletUtil.getClientIP(request);

        // 转换为原有的订单创建请求格式
        cn.iocoder.yudao.module.wbclass.controller.app.course.vo.AppWbClassCourseOrderCreateReqVO orderCreateReqVO =
            new cn.iocoder.yudao.module.wbclass.controller.app.course.vo.AppWbClassCourseOrderCreateReqVO();
        orderCreateReqVO.setSkuId(createReqVO.getSkuId());
        orderCreateReqVO.setUserRemark(createReqVO.getUserRemark());

        return success(orderService.createOrder(orderCreateReqVO, userId, userIp));
    }

}
