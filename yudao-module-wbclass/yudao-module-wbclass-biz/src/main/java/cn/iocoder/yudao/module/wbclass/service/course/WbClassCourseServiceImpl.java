package cn.iocoder.yudao.module.wbclass.service.course;

import cn.hutool.core.collection.ListUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.wbclass.controller.admin.course.vo.WbClassCourseCreateReqVO;
import cn.iocoder.yudao.module.wbclass.controller.admin.course.vo.WbClassCoursePageReqVO;
import cn.iocoder.yudao.module.wbclass.controller.admin.course.vo.WbClassCourseUpdateReqVO;
import cn.iocoder.yudao.module.wbclass.controller.app.course.vo.AppWbClassCoursePageReqVO;
import cn.iocoder.yudao.module.wbclass.controller.app.course.vo.AppWbClassCourseRespVO;
import cn.iocoder.yudao.module.wbclass.convert.course.WbClassCourseConvert;
import cn.iocoder.yudao.module.wbclass.dal.dataobject.course.WbClassCourseDO;
import cn.iocoder.yudao.module.wbclass.dal.dataobject.product.WbClassCourseProductSkuDO;
import cn.iocoder.yudao.module.wbclass.dal.mysql.course.WbClassCourseMapper;
import cn.iocoder.yudao.module.wbclass.enums.CourseStatusEnum;
import cn.iocoder.yudao.module.wbclass.service.product.WbClassCourseProductSkuService;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.wbclass.enums.ErrorCodeConstants.*;

/**
 * 练习营课程产品 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class WbClassCourseServiceImpl implements WbClassCourseService {

    @Resource
    private WbClassCourseMapper courseMapper;

    @Resource
    private WbClassCourseProductSkuService productSkuService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createCourse(WbClassCourseCreateReqVO createReqVO) {
        // 转换对象
        WbClassCourseDO course = WbClassCourseConvert.INSTANCE.convert(createReqVO);
        // 插入
        courseMapper.insert(course);

        // 返回
        return course.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateCourse(WbClassCourseUpdateReqVO updateReqVO) {
        // 校验存在
        validateCourseExists(updateReqVO.getId());

        // 更新
        WbClassCourseDO updateObj = WbClassCourseConvert.INSTANCE.convert(updateReqVO);
        courseMapper.updateById(updateObj);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteCourse(Long id) {
        // 校验存在
        validateCourseExists(id);
        // 删除课程
        courseMapper.deleteById(id);
    }

    private void validateCourseExists(Long id) {
        if (courseMapper.selectById(id) == null) {
            throw exception(COURSE_NOT_EXISTS);
        }
    }

    @Override
    public WbClassCourseDO getCourse(Long id) {
        return courseMapper.selectById(id);
    }

    @Override
    public List<WbClassCourseDO> getCourseList(Collection<Long> ids) {
        return courseMapper.selectBatchIds(ids);
    }

    @Override
    public PageResult<WbClassCourseDO> getCoursePage(WbClassCoursePageReqVO pageReqVO) {
        return courseMapper.selectPage(pageReqVO);
    }

    @Override
    public PageResult<WbClassCourseDO> getAppCoursePage(AppWbClassCoursePageReqVO pageReqVO) {
        return courseMapper.selectPage(pageReqVO, new LambdaQueryWrapperX<WbClassCourseDO>()
                .likeIfPresent(WbClassCourseDO::getName, pageReqVO.getName())
                .eqIfPresent(WbClassCourseDO::getStatus, CourseStatusEnum.ON_SALE.getStatus()) // 只显示上架的课程
                .orderByDesc(WbClassCourseDO::getSort)
                .orderByDesc(WbClassCourseDO::getId));
    }

    @Override
    public List<AppWbClassCourseRespVO> fillCoursePriceInfo(List<WbClassCourseDO> courses) {
        if (courses == null || courses.isEmpty()) {
            return ListUtil.empty();
        }

        // 获取所有课程ID
        List<Long> courseIds = courses.stream()
                .map(WbClassCourseDO::getId)
                .collect(Collectors.toList());

        // 批量获取课程关联的SKU信息
        Map<Long, List<WbClassCourseProductSkuDO>> courseSkuMap = productSkuService.getSkusByCourseIds(courseIds);

        // 转换并填充价格信息
        return courses.stream().map(course -> {
            AppWbClassCourseRespVO respVO = WbClassCourseConvert.INSTANCE.convertApp(course);

            // 获取该课程关联的SKU列表
            List<WbClassCourseProductSkuDO> skus = courseSkuMap.get(course.getId());
            if (skus != null && !skus.isEmpty()) {
                // 计算价格信息
                Integer minOriginalPrice = skus.stream()
                        .map(WbClassCourseProductSkuDO::getOriginalPrice)
                        .filter(price -> price != null && price > 0)
                        .min(Integer::compareTo)
                        .orElse(null);

                Integer maxOriginalPrice = skus.stream()
                        .map(WbClassCourseProductSkuDO::getOriginalPrice)
                        .filter(price -> price != null && price > 0)
                        .max(Integer::compareTo)
                        .orElse(null);

                Integer minSalePrice = skus.stream()
                        .map(WbClassCourseProductSkuDO::getSalePrice)
                        .filter(price -> price != null && price > 0)
                        .min(Integer::compareTo)
                        .orElse(null);

                Integer maxSalePrice = skus.stream()
                        .map(WbClassCourseProductSkuDO::getSalePrice)
                        .filter(price -> price != null && price > 0)
                        .max(Integer::compareTo)
                        .orElse(null);

                // 设置价格信息
                if (skus.size() == 1) {
                    // 只有一个SKU，显示具体价格
                    WbClassCourseProductSkuDO sku = skus.get(0);
                    respVO.setOriginalPrice(sku.getOriginalPrice());
                    respVO.setSalePrice(sku.getSalePrice());
                    respVO.setHasMultipleSkus(false);
                } else {
                    // 多个SKU，显示价格区间
                    respVO.setMinPrice(minSalePrice);
                    respVO.setMaxPrice(maxSalePrice);
                    respVO.setHasMultipleSkus(true);
                    // 对于多SKU情况，可以显示最低售价作为主要价格
                    respVO.setSalePrice(minSalePrice);
                    respVO.setOriginalPrice(minOriginalPrice);
                }
            }

            return respVO;
        }).collect(Collectors.toList());
    }

}
