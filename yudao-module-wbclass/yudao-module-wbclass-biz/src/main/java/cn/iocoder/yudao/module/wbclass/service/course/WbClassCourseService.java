package cn.iocoder.yudao.module.wbclass.service.course;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.wbclass.controller.admin.course.vo.WbClassCourseCreateReqVO;
import cn.iocoder.yudao.module.wbclass.controller.admin.course.vo.WbClassCoursePageReqVO;
import cn.iocoder.yudao.module.wbclass.controller.admin.course.vo.WbClassCourseUpdateReqVO;
import cn.iocoder.yudao.module.wbclass.controller.app.course.vo.AppWbClassCoursePageReqVO;
import cn.iocoder.yudao.module.wbclass.controller.app.course.vo.AppWbClassCourseRespVO;
import cn.iocoder.yudao.module.wbclass.dal.dataobject.course.WbClassCourseDO;

import javax.validation.Valid;
import java.util.Collection;
import java.util.List;

/**
 * 练习营课程产品 Service 接口
 *
 * <AUTHOR>
 */
public interface WbClassCourseService {

    /**
     * 创建课程产品
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createCourse(@Valid WbClassCourseCreateReqVO createReqVO);

    /**
     * 更新课程产品
     *
     * @param updateReqVO 更新信息
     */
    void updateCourse(@Valid WbClassCourseUpdateReqVO updateReqVO);

    /**
     * 删除课程产品
     *
     * @param id 编号
     */
    void deleteCourse(Long id);

    /**
     * 获得课程产品
     *
     * @param id 编号
     * @return 课程产品
     */
    WbClassCourseDO getCourse(Long id);

    /**
     * 获得课程产品列表
     *
     * @param ids 编号
     * @return 课程产品列表
     */
    List<WbClassCourseDO> getCourseList(Collection<Long> ids);

    /**
     * 获得课程产品分页
     *
     * @param pageReqVO 分页查询
     * @return 课程产品分页
     */
    PageResult<WbClassCourseDO> getCoursePage(WbClassCoursePageReqVO pageReqVO);

    /**
     * 获得App端课程产品分页
     *
     * @param pageReqVO 分页查询
     * @return 课程产品分页
     */
    PageResult<WbClassCourseDO> getAppCoursePage(AppWbClassCoursePageReqVO pageReqVO);

    /**
     * 填充课程列表的价格信息
     *
     * @param courses 课程列表
     * @return 带价格信息的课程响应列表
     */
    List<AppWbClassCourseRespVO> fillCoursePriceInfo(List<WbClassCourseDO> courses);

}