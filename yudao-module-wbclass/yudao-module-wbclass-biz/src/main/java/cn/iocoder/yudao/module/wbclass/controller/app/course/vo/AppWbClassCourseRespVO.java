package cn.iocoder.yudao.module.wbclass.controller.app.course.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

@ApiModel("用户 App - 练习营课程产品 Response VO")
@Data
public class AppWbClassCourseRespVO {

    @ApiModelProperty(value = "课程产品ID", example = "1024")
    private Long id;

    @ApiModelProperty(value = "课程名称", example = "Java全栈开发练习营")
    private String name;

    @ApiModelProperty(value = "课程封面图片", example = "https://example.com/cover.jpg")
    private String coverUrl;

    @ApiModelProperty(value = "课程详情描述", example = "这是一个全面的Java开发课程...")
    private String description;

    @ApiModelProperty(value = "课程简介", example = "适合零基础学员的Java课程")
    private String shortDescription;

    @ApiModelProperty(value = "状态：1-上架，2-下架", example = "1")
    private Integer status;

    @ApiModelProperty(value = "创建时间", example = "2024-01-01 12:00:00")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "原价，单位：分", example = "19900")
    private Integer originalPrice;

    @ApiModelProperty(value = "售价，单位：分", example = "9900")
    private Integer salePrice;

    @ApiModelProperty(value = "最低价格，单位：分（多SKU时显示）", example = "9900")
    private Integer minPrice;

    @ApiModelProperty(value = "最高价格，单位：分（多SKU时显示）", example = "19900")
    private Integer maxPrice;

    @ApiModelProperty(value = "是否有多个价格规格", example = "false")
    private Boolean hasMultipleSkus;

}