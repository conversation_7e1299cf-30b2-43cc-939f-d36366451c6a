-- 测试数据：课程分页接口价格信息

-- 1. 查看现有课程数据
SELECT id, name, status, create_time 
FROM edusys_wbclass_course 
WHERE status = 1 
ORDER BY sort DESC, id DESC 
LIMIT 10;

-- 2. 查看课程关联的SKU数据
SELECT 
    c.id as course_id,
    c.name as course_name,
    csr.sku_id,
    sku.sku_name,
    sku.original_price,
    sku.sale_price,
    sku.status as sku_status
FROM edusys_wbclass_course c
LEFT JOIN edusys_wbclass_course_sku_relation csr ON c.id = csr.course_id
LEFT JOIN edusys_wbclass_course_product_sku sku ON csr.sku_id = sku.id
WHERE c.status = 1
ORDER BY c.id, csr.sort;

-- 3. 查看没有关联SKU的课程
SELECT 
    c.id as course_id,
    c.name as course_name
FROM edusys_wbclass_course c
LEFT JOIN edusys_wbclass_course_sku_relation csr ON c.id = csr.course_id
WHERE c.status = 1 AND csr.id IS NULL;

-- 4. 查看有多个SKU的课程
SELECT 
    c.id as course_id,
    c.name as course_name,
    COUNT(csr.sku_id) as sku_count,
    MIN(sku.sale_price) as min_price,
    MAX(sku.sale_price) as max_price
FROM edusys_wbclass_course c
INNER JOIN edusys_wbclass_course_sku_relation csr ON c.id = csr.course_id
INNER JOIN edusys_wbclass_course_product_sku sku ON csr.sku_id = sku.id
WHERE c.status = 1 AND sku.status = 1
GROUP BY c.id, c.name
HAVING COUNT(csr.sku_id) > 1;

-- 5. 插入测试数据（如果需要）
-- 注意：以下INSERT语句仅在需要测试数据时执行

-- 插入测试课程
-- INSERT INTO edusys_wbclass_course (name, cover_url, description, short_description, sort, status, creator, create_time, updater, update_time, deleted)
-- VALUES 
-- ('Java全栈开发训练营', 'https://example.com/java-cover.jpg', '全面的Java开发课程', '适合零基础学员', 100, 1, 'admin', NOW(), 'admin', NOW(), 0),
-- ('Python数据分析课程', 'https://example.com/python-cover.jpg', 'Python数据分析实战', '数据分析入门', 90, 1, 'admin', NOW(), 'admin', NOW(), 0);

-- 插入测试商品
-- INSERT INTO edusys_wbclass_course_product (name, description, cover_url, original_price, sale_price, view_count, sales_count, status, sort, marketing_tags, creator, create_time, updater, update_time, deleted)
-- VALUES 
-- ('Java训练营商品', 'Java全栈开发训练营商品', 'https://example.com/java-product.jpg', 19900, 9900, 0, 0, 1, 10, '热门,推荐', 'admin', NOW(), 'admin', NOW(), 0);

-- 插入测试SKU
-- INSERT INTO edusys_wbclass_course_product_sku (product_id, sku_name, sku_code, original_price, sale_price, stock, sales_count, sort, status, spec_info, creator, create_time, updater, update_time, deleted)
-- VALUES 
-- (1, '基础版', 'JAVA_BASIC', 9900, 6900, 999, 0, 1, 1, '{"level":"基础"}', 'admin', NOW(), 'admin', NOW(), 0),
-- (1, '进阶版', 'JAVA_ADVANCED', 19900, 14900, 999, 0, 2, 1, '{"level":"进阶"}', 'admin', NOW(), 'admin', NOW(), 0);

-- 插入课程SKU关联关系
-- INSERT INTO edusys_wbclass_course_sku_relation (sku_id, course_id, sort, creator, create_time, updater, update_time, deleted)
-- VALUES 
-- (1, 1, 1, 'admin', NOW(), 'admin', NOW(), 0),
-- (2, 1, 2, 'admin', NOW(), 'admin', NOW(), 0);
