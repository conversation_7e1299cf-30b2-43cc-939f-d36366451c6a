package cn.iocoder.yudao.module.wbclass.service.course;

import cn.iocoder.yudao.module.wbclass.controller.app.course.vo.AppWbClassCourseRespVO;
import cn.iocoder.yudao.module.wbclass.dal.dataobject.course.WbClassCourseDO;
import cn.iocoder.yudao.module.wbclass.dal.dataobject.product.WbClassCourseProductSkuDO;
import cn.iocoder.yudao.module.wbclass.service.product.WbClassCourseProductSkuService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * 课程服务测试类 - 验证价格信息填充功能
 */
@ExtendWith(MockitoExtension.class)
public class CourseServiceTest {

    @Mock
    private WbClassCourseProductSkuService productSkuService;

    @InjectMocks
    private WbClassCourseServiceImpl courseService;

    @Test
    public void testFillCoursePriceInfo_SingleSku() {
        // 准备测试数据
        WbClassCourseDO course = WbClassCourseDO.builder()
                .id(1L)
                .name("Java基础课程")
                .coverUrl("https://example.com/cover.jpg")
                .description("Java基础课程描述")
                .shortDescription("适合初学者")
                .status(1)
                .createTime(LocalDateTime.now())
                .build();

        WbClassCourseProductSkuDO sku = WbClassCourseProductSkuDO.builder()
                .id(1L)
                .skuName("基础版")
                .originalPrice(19900)
                .salePrice(9900)
                .status(1)
                .build();

        Map<Long, List<WbClassCourseProductSkuDO>> courseSkuMap = new HashMap<>();
        courseSkuMap.put(1L, Arrays.asList(sku));

        // Mock 方法调用
        when(productSkuService.getSkusByCourseIds(Arrays.asList(1L)))
                .thenReturn(courseSkuMap);

        // 执行测试
        List<AppWbClassCourseRespVO> result = courseService.fillCoursePriceInfo(Arrays.asList(course));

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());

        AppWbClassCourseRespVO respVO = result.get(0);
        assertEquals(1L, respVO.getId());
        assertEquals("Java基础课程", respVO.getName());
        assertEquals(19900, respVO.getOriginalPrice());
        assertEquals(9900, respVO.getSalePrice());
        assertNull(respVO.getMinPrice());
        assertNull(respVO.getMaxPrice());
        assertFalse(respVO.getHasMultipleSkus());
    }

    @Test
    public void testFillCoursePriceInfo_MultipleSku() {
        // 准备测试数据
        WbClassCourseDO course = WbClassCourseDO.builder()
                .id(2L)
                .name("Java进阶课程")
                .coverUrl("https://example.com/cover2.jpg")
                .description("Java进阶课程描述")
                .shortDescription("适合有基础的学员")
                .status(1)
                .createTime(LocalDateTime.now())
                .build();

        WbClassCourseProductSkuDO sku1 = WbClassCourseProductSkuDO.builder()
                .id(1L)
                .skuName("基础版")
                .originalPrice(19900)
                .salePrice(9900)
                .status(1)
                .build();

        WbClassCourseProductSkuDO sku2 = WbClassCourseProductSkuDO.builder()
                .id(2L)
                .skuName("高级版")
                .originalPrice(39900)
                .salePrice(19900)
                .status(1)
                .build();

        Map<Long, List<WbClassCourseProductSkuDO>> courseSkuMap = new HashMap<>();
        courseSkuMap.put(2L, Arrays.asList(sku1, sku2));

        // Mock 方法调用
        when(productSkuService.getSkusByCourseIds(Arrays.asList(2L)))
                .thenReturn(courseSkuMap);

        // 执行测试
        List<AppWbClassCourseRespVO> result = courseService.fillCoursePriceInfo(Arrays.asList(course));

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());

        AppWbClassCourseRespVO respVO = result.get(0);
        assertEquals(2L, respVO.getId());
        assertEquals("Java进阶课程", respVO.getName());
        assertEquals(19900, respVO.getOriginalPrice()); // 最低原价
        assertEquals(9900, respVO.getSalePrice()); // 最低售价
        assertEquals(9900, respVO.getMinPrice()); // 最低价格
        assertEquals(19900, respVO.getMaxPrice()); // 最高价格
        assertTrue(respVO.getHasMultipleSkus());
    }

    @Test
    public void testFillCoursePriceInfo_NoSku() {
        // 准备测试数据
        WbClassCourseDO course = WbClassCourseDO.builder()
                .id(3L)
                .name("免费体验课")
                .coverUrl("https://example.com/cover3.jpg")
                .description("免费体验课程")
                .shortDescription("完全免费")
                .status(1)
                .createTime(LocalDateTime.now())
                .build();

        Map<Long, List<WbClassCourseProductSkuDO>> courseSkuMap = new HashMap<>();
        // 没有SKU关联

        // Mock 方法调用
        when(productSkuService.getSkusByCourseIds(Arrays.asList(3L)))
                .thenReturn(courseSkuMap);

        // 执行测试
        List<AppWbClassCourseRespVO> result = courseService.fillCoursePriceInfo(Arrays.asList(course));

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());

        AppWbClassCourseRespVO respVO = result.get(0);
        assertEquals(3L, respVO.getId());
        assertEquals("免费体验课", respVO.getName());
        assertNull(respVO.getOriginalPrice());
        assertNull(respVO.getSalePrice());
        assertNull(respVO.getMinPrice());
        assertNull(respVO.getMaxPrice());
        assertFalse(respVO.getHasMultipleSkus());
    }

    @Test
    public void testFillCoursePriceInfo_EmptyList() {
        // 执行测试
        List<AppWbClassCourseRespVO> result = courseService.fillCoursePriceInfo(new ArrayList<>());

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());

        // 验证没有调用SKU服务
        verify(productSkuService, never()).getSkusByCourseIds(any());
    }

    @Test
    public void testFillCoursePriceInfo_NullInput() {
        // 执行测试
        List<AppWbClassCourseRespVO> result = courseService.fillCoursePriceInfo(null);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());

        // 验证没有调用SKU服务
        verify(productSkuService, never()).getSkusByCourseIds(any());
    }
}
