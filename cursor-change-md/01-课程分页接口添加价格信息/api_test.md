# API测试文档

## 接口信息

**接口地址**: `GET /wbclass/course/page`

**请求参数**:
- `pageNo`: 页码（默认1）
- `pageSize`: 每页大小（默认10）
- `name`: 课程名称（可选，模糊搜索）

## 修改前的响应格式

```json
{
  "code": 0,
  "data": {
    "list": [
      {
        "id": 1,
        "name": "Think单词营",
        "coverUrl": "https://example.com/cover.jpg",
        "description": "系统化学英语单词，掌握...",
        "shortDescription": "适合零基础学员",
        "status": 1,
        "createTime": "2024-01-01T12:00:00"
        // 缺少价格信息，导致前端显示YNaN
      }
    ],
    "total": 4
  },
  "msg": "操作成功"
}
```

## 修改后的响应格式

### 单SKU课程示例

```json
{
  "code": 0,
  "data": {
    "list": [
      {
        "id": 1,
        "name": "Think单词营",
        "coverUrl": "https://example.com/cover.jpg",
        "description": "系统化学英语单词，掌握...",
        "shortDescription": "适合零基础学员",
        "status": 1,
        "createTime": "2024-01-01T12:00:00",
        "originalPrice": 19900,
        "salePrice": 9900,
        "minPrice": null,
        "maxPrice": null,
        "hasMultipleSkus": false
      }
    ],
    "total": 4
  },
  "msg": "操作成功"
}
```

### 多SKU课程示例

```json
{
  "code": 0,
  "data": {
    "list": [
      {
        "id": 2,
        "name": "Java全栈开发训练营",
        "coverUrl": "https://example.com/java-cover.jpg",
        "description": "全面的Java开发课程",
        "shortDescription": "适合有基础的学员",
        "status": 1,
        "createTime": "2024-01-01T12:00:00",
        "originalPrice": 9900,
        "salePrice": 6900,
        "minPrice": 6900,
        "maxPrice": 14900,
        "hasMultipleSkus": true
      }
    ],
    "total": 4
  },
  "msg": "操作成功"
}
```

### 无SKU课程示例

```json
{
  "code": 0,
  "data": {
    "list": [
      {
        "id": 3,
        "name": "免费体验课",
        "coverUrl": "https://example.com/free-cover.jpg",
        "description": "免费体验课程",
        "shortDescription": "完全免费",
        "status": 1,
        "createTime": "2024-01-01T12:00:00",
        "originalPrice": null,
        "salePrice": null,
        "minPrice": null,
        "maxPrice": null,
        "hasMultipleSkus": false
      }
    ],
    "total": 4
  },
  "msg": "操作成功"
}
```

## 测试步骤

1. **启动应用**
   ```bash
   # 确保应用正常启动
   mvn spring-boot:run
   ```

2. **测试基本分页**
   ```bash
   curl -X GET "http://localhost:8080/wbclass/course/page?pageNo=1&pageSize=10"
   ```

3. **测试名称搜索**
   ```bash
   curl -X GET "http://localhost:8080/wbclass/course/page?pageNo=1&pageSize=10&name=Java"
   ```

4. **验证价格字段**
   - 检查响应中是否包含 `originalPrice`、`salePrice` 等价格字段
   - 验证单SKU课程的价格显示
   - 验证多SKU课程的价格区间显示
   - 验证无SKU课程的价格为空

## 前端显示效果

修改后，前端应该能够：
1. 正确显示课程价格，不再出现"YNaN"
2. 对于多SKU课程，可以显示价格区间（如：¥69.00-¥149.00）
3. 对于单SKU课程，显示具体价格（如：¥99.00）
4. 对于免费课程，可以显示"免费"或隐藏价格

## 注意事项

1. **价格单位**: 所有价格字段的单位都是分，前端需要除以100显示为元
2. **空值处理**: 前端需要处理价格为null的情况
3. **多SKU显示**: 当`hasMultipleSkus`为true时，建议显示价格区间
4. **性能**: 使用了批量查询优化，避免N+1查询问题
