# 课程分页接口添加价格信息

## 问题描述

课程分页接口 `wbclass/course/page?pageNo=1&pageSize=10` 没有返回价格相关信息，导致前端界面显示"YNaN"，无法正确显示课程价格。

## 问题分析

1. **数据结构分析**：

   - `WbClassCourseDO`（课程实体）本身没有价格字段
   - 价格信息存储在 `WbClassCourseProductSkuDO`（课程商品 SKU）中，包含 `originalPrice` 和 `salePrice` 字段
   - 课程和 SKU 通过 `WbClassCourseSkuRelationDO` 进行关联

2. **当前问题**：
   - `AppWbClassCourseRespVO` 没有价格相关字段
   - 课程分页接口只返回课程基本信息，不包含价格信息

## 解决方案

### 1. 修改响应 VO 添加价格字段

在 `AppWbClassCourseRespVO` 中添加价格相关字段：

- `originalPrice`: 原价（单位：分）
- `salePrice`: 售价（单位：分）
- `minPrice`: 最低价格（多 SKU 时显示）
- `maxPrice`: 最高价格（多 SKU 时显示）
- `hasMultipleSkus`: 是否有多个价格规格

### 2. 扩展 SKU 服务

在 `WbClassCourseProductSkuService` 中添加方法：

- `getSkusByCourseId(Long courseId)`: 根据课程 ID 获取关联的 SKU 列表
- `getSkusByCourseIds(Collection<Long> courseIds)`: 批量获取课程关联的 SKU 列表（避免 N+1 查询问题）

### 3. 修改课程服务

在 `WbClassCourseService` 中添加方法：

- `fillCoursePriceInfo(List<WbClassCourseDO> courses)`: 填充课程列表的价格信息

### 4. 更新控制器

修改 `AppWbClassCourseController` 的 `/page` 接口，在返回前填充价格信息。

## 价格显示策略

1. **单 SKU 课程**：

   - 显示具体的原价和售价
   - `hasMultipleSkus = false`

2. **多 SKU 课程**：

   - 显示价格区间（最低价-最高价）
   - `hasMultipleSkus = true`
   - 主要价格显示最低售价

3. **无 SKU 课程**：
   - 价格字段为空

## 修改的文件

1. `AppWbClassCourseRespVO.java` - 添加价格字段
2. `WbClassCourseProductSkuService.java` - 添加接口方法
3. `WbClassCourseProductSkuServiceImpl.java` - 实现新方法
4. `WbClassCourseService.java` - 添加价格填充方法
5. `WbClassCourseServiceImpl.java` - 实现价格填充逻辑
6. `AppWbClassCourseController.java` - 修改分页接口

## 接口测试

修改完成后，访问 `wbclass/course/page?pageNo=1&pageSize=10` 接口应该返回包含价格信息的课程列表，前端界面应该能正确显示价格而不是"YNaN"。

## 性能优化

- 使用批量查询避免 N+1 查询问题
- 在内存中组装数据，减少数据库查询次数

## 实现细节

### 1. 数据库查询优化

通过 `getSkusByCourseIds` 方法实现批量查询：

```java
// 批量获取所有课程的SKU关联关系
List<WbClassCourseSkuRelationDO> allRelations = relationMapper.selectList(
    new LambdaQueryWrapperX<WbClassCourseSkuRelationDO>()
        .in(WbClassCourseSkuRelationDO::getCourseId, courseIds)
        .orderByAsc(WbClassCourseSkuRelationDO::getSort)
);

// 批量查询所有SKU
List<WbClassCourseProductSkuDO> allSkus = skuMapper.selectBatchIds(allSkuIds);
```

### 2. 价格计算逻辑

```java
// 单SKU课程：显示具体价格
if (skus.size() == 1) {
    WbClassCourseProductSkuDO sku = skus.get(0);
    respVO.setOriginalPrice(sku.getOriginalPrice());
    respVO.setSalePrice(sku.getSalePrice());
    respVO.setHasMultipleSkus(false);
} else {
    // 多SKU课程：显示价格区间
    respVO.setMinPrice(minSalePrice);
    respVO.setMaxPrice(maxSalePrice);
    respVO.setHasMultipleSkus(true);
    respVO.setSalePrice(minSalePrice); // 主要价格显示最低售价
    respVO.setOriginalPrice(minOriginalPrice);
}
```

### 3. 前端适配建议

前端可以根据 `hasMultipleSkus` 字段来决定显示方式：

```javascript
// 单SKU显示
if (!course.hasMultipleSkus && course.salePrice) {
  return `¥${(course.salePrice / 100).toFixed(2)}`;
}

// 多SKU显示价格区间
if (course.hasMultipleSkus && course.minPrice && course.maxPrice) {
  if (course.minPrice === course.maxPrice) {
    return `¥${(course.minPrice / 100).toFixed(2)}`;
  } else {
    return `¥${(course.minPrice / 100).toFixed(2)}-¥${(
      course.maxPrice / 100
    ).toFixed(2)}`;
  }
}

// 免费或无价格
return "免费";
```

## 测试验证

已创建单元测试 `CourseServiceTest.java` 验证以下场景：

1. 单 SKU 课程的价格填充
2. 多 SKU 课程的价格区间计算
3. 无 SKU 课程的处理
4. 空列表和 null 输入的处理

## 部署说明

1. 确保数据库中有课程和 SKU 的关联数据
2. 重新编译并部署应用
3. 测试接口返回是否包含价格信息
4. 验证前端界面不再显示"YNaN"
